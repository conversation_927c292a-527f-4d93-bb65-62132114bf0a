import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class MigratePostsToSkills {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Migrate all posts from 'posts' collection to 'skills' collection
  static Future<void> migratePostsToSkills() async {
    try {
      debugPrint('🔄 Starting migration from posts to skills...');
      
      // Get all documents from posts collection
      final postsSnapshot = await _firestore.collection('posts').get();
      debugPrint('📊 Found ${postsSnapshot.docs.length} posts to migrate');
      
      if (postsSnapshot.docs.isEmpty) {
        debugPrint('✅ No posts found to migrate');
        return;
      }

      int migratedCount = 0;
      int errorCount = 0;

      // Migrate each post to skills collection
      for (final doc in postsSnapshot.docs) {
        try {
          final data = doc.data();
          
          // Add the document to skills collection
          await _firestore.collection('skills').add(data);
          
          migratedCount++;
          debugPrint('✅ Migrated post: ${data['title']} (${doc.id})');
          
        } catch (e) {
          errorCount++;
          debugPrint('❌ Error migrating post ${doc.id}: $e');
        }
      }

      debugPrint('🎉 Migration completed!');
      debugPrint('✅ Successfully migrated: $migratedCount posts');
      debugPrint('❌ Errors: $errorCount posts');
      
      // Optionally delete the old posts collection after successful migration
      if (errorCount == 0) {
        debugPrint('🗑️ All posts migrated successfully. You can now delete the posts collection manually if desired.');
      }
      
    } catch (e) {
      debugPrint('❌ Migration failed: $e');
    }
  }

  // Check if migration is needed
  static Future<bool> isMigrationNeeded() async {
    try {
      final postsSnapshot = await _firestore.collection('posts').limit(1).get();
      final skillsSnapshot = await _firestore.collection('skills').limit(1).get();
      
      final hasPosts = postsSnapshot.docs.isNotEmpty;
      final hasSkills = skillsSnapshot.docs.isNotEmpty;
      
      debugPrint('📊 Migration check: Posts: $hasPosts, Skills: $hasSkills');
      
      // Migration needed if there are posts but no skills
      return hasPosts && !hasSkills;
    } catch (e) {
      debugPrint('❌ Error checking migration status: $e');
      return false;
    }
  }

  // Get counts for both collections
  static Future<Map<String, int>> getCollectionCounts() async {
    try {
      final postsSnapshot = await _firestore.collection('posts').get();
      final skillsSnapshot = await _firestore.collection('skills').get();
      
      return {
        'posts': postsSnapshot.docs.length,
        'skills': skillsSnapshot.docs.length,
      };
    } catch (e) {
      debugPrint('❌ Error getting collection counts: $e');
      return {'posts': 0, 'skills': 0};
    }
  }

  // Create a test skill for debugging
  static Future<void> createTestSkill() async {
    try {
      await _firestore.collection('skills').add({
        'title': 'Test Flutter Development',
        'description': 'Learn Flutter app development from scratch',
        'category': 'Programming',
        'type': 'offer',
        'duration': '2 hours',
        'location': 'Online',
        'mode': 'Virtual',
        'experienceLevel': 'Beginner',
        'userId': 'test-user-123',
        'userEmail': '<EMAIL>',
        'timestamp': FieldValue.serverTimestamp(),
      });
      
      debugPrint('✅ Test skill created successfully');
    } catch (e) {
      debugPrint('❌ Error creating test skill: $e');
    }
  }
}
