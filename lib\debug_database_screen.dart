import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'migrate_posts_to_skills.dart';

class DebugDatabaseScreen extends StatefulWidget {
  const DebugDatabaseScreen({super.key});

  @override
  State<DebugDatabaseScreen> createState() => _DebugDatabaseScreenState();
}

class _DebugDatabaseScreenState extends State<DebugDatabaseScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  List<Map<String, dynamic>> _skillsData = [];
  List<Map<String, dynamic>> _postsData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Check skills collection
      final skillsSnapshot = await _firestore.collection('skills').get();
      _skillsData =
          skillsSnapshot.docs.map((doc) {
            final data = doc.data();
            data['docId'] = doc.id;
            return data;
          }).toList();

      // Check posts collection (old)
      final postsSnapshot = await _firestore.collection('posts').get();
      _postsData =
          postsSnapshot.docs.map((doc) {
            final data = doc.data();
            data['docId'] = doc.id;
            return data;
          }).toList();

      setState(() => _isLoading = false);

      debugPrint('🔍 Found ${_skillsData.length} skills and ${_postsData.length} posts');
    } catch (e) {
      debugPrint('❌ Error loading data: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _createTestSkill() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please log in first')));
        return;
      }

      await _firestore.collection('skills').add({
        'title': 'Test Programming Skill',
        'description': 'This is a test skill for debugging',
        'category': 'Programming',
        'type': 'offer',
        'duration': '1 hour',
        'location': 'Online',
        'mode': 'Virtual',
        'experienceLevel': 'Beginner',
        'userId': user.uid,
        'userEmail': user.email,
        'timestamp': FieldValue.serverTimestamp(),
      });

      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Test skill created!')));

      _loadData(); // Refresh data
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error creating test skill: $e')));
      }
    }
  }

  Future<void> _migrateData() async {
    try {
      // Check if migration is needed
      final counts = await MigratePostsToSkills.getCollectionCounts();

      if (counts['posts'] == 0) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('No posts found to migrate')));
        }
        return;
      }

      // Show confirmation dialog
      final shouldMigrate = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Migrate Data'),
              content: Text(
                'Found ${counts['posts']} posts and ${counts['skills']} skills.\n\nMigrate all posts to skills collection?',
              ),
              actions: [
                TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Cancel')),
                ElevatedButton(onPressed: () => Navigator.pop(context, true), child: const Text('Migrate')),
              ],
            ),
      );

      if (shouldMigrate == true) {
        await MigratePostsToSkills.migratePostsToSkills();
        _loadData(); // Refresh data

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Migration completed!')));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Migration failed: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Debug'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.add), onPressed: _createTestSkill, tooltip: 'Create Test Skill'),
          IconButton(icon: const Icon(Icons.sync), onPressed: _migrateData, tooltip: 'Migrate Posts to Skills'),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData, tooltip: 'Refresh'),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current user info
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('Current User:', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('UID: ${_auth.currentUser?.uid ?? 'Not logged in'}'),
                            Text('Email: ${_auth.currentUser?.email ?? 'Not logged in'}'),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Skills collection
                    Text(
                      'Skills Collection (${_skillsData.length} items)',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    if (_skillsData.isEmpty)
                      const Card(
                        child: Padding(padding: EdgeInsets.all(16), child: Text('No skills found in database')),
                      )
                    else
                      ..._skillsData.map(
                        (skill) => Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Title: ${skill['title'] ?? 'N/A'}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text('Type: ${skill['type'] ?? 'N/A'}'),
                                Text('Category: ${skill['category'] ?? 'N/A'}'),
                                Text('User: ${skill['userEmail'] ?? 'N/A'}'),
                                Text('UserID: ${skill['userId'] ?? 'N/A'}'),
                                Text('Doc ID: ${skill['docId']}'),
                                Text('Timestamp: ${skill['timestamp']?.toString() ?? 'N/A'}'),
                              ],
                            ),
                          ),
                        ),
                      ),

                    const SizedBox(height: 24),

                    // Posts collection (old)
                    Text(
                      'Posts Collection (${_postsData.length} items)',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    if (_postsData.isEmpty)
                      const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('No posts found in database')))
                    else
                      ..._postsData.map(
                        (post) => Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Title: ${post['title'] ?? 'N/A'}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text('Type: ${post['type'] ?? 'N/A'}'),
                                Text('Category: ${post['category'] ?? 'N/A'}'),
                                Text('User: ${post['userEmail'] ?? 'N/A'}'),
                                Text('UserID: ${post['userId'] ?? 'N/A'}'),
                                Text('Doc ID: ${post['docId']}'),
                                Text('Timestamp: ${post['timestamp']?.toString() ?? 'N/A'}'),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
    );
  }
}
