import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'theme/app_theme.dart';
import 'chat_screen.dart';
import 'session_scheduling_screen.dart';
import 'services/firestore_stream_service.dart';

class ComprehensiveSkillMarketplaceScreen extends StatefulWidget {
  const ComprehensiveSkillMarketplaceScreen({super.key});

  @override
  State<ComprehensiveSkillMarketplaceScreen> createState() => _ComprehensiveSkillMarketplaceScreenState();
}

class _ComprehensiveSkillMarketplaceScreenState extends State<ComprehensiveSkillMarketplaceScreen>
    with AutomaticKeepAliveClientMixin {
  final FirestoreStreamService _streamService = FirestoreStreamService();
  String searchQuery = '';
  String? selectedCategory;
  String? selectedMode;
  String? selectedLevel;
  bool showFilters = false;

  final List<String> categories = [
    'Programming',
    'Design',
    'Languages',
    'Music',
    'Sports',
    'Cooking',
    'Photography',
    'Writing',
    'Mathematics',
    'Science',
    'Art',
    'Other',
  ];

  final List<String> modes = ['Online', 'In-person', 'Both'];
  final List<String> levels = ['Beginner', 'Intermediate', 'Advanced', 'All levels'];

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _streamService.disposeStream('marketplace');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          _buildSearchBar(false),
          if (showFilters) _buildFilterPanel(false),
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: _streamService.getSkillsStream('marketplace', excludeCurrentUser: true),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  debugPrint('Marketplace StreamBuilder Error: ${snapshot.error}');
                  return _buildErrorState(snapshot.error.toString());
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator(color: AppTheme.primaryGreen));
                }

                final skills = snapshot.data?.docs ?? [];
                final groupedSkills = _groupSkillsByUser(skills);
                final filteredSkills = _filterSkills(groupedSkills);

                if (filteredSkills.isEmpty) {
                  return _buildEmptyState(false);
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(24),
                  itemCount: filteredSkills.length,
                  itemBuilder: (context, index) => _buildUserSkillCard(filteredSkills[index], false),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: "Search skills, users, or categories...",
                hintStyle: TextStyle(color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600),
                prefixIcon: const Icon(Icons.search_rounded, color: Colors.deepPurple),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              ),
              style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
              onChanged: (val) => setState(() => searchQuery = val),
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              onPressed: () => setState(() => showFilters = !showFilters),
              icon: Icon(
                showFilters ? Icons.filter_list_off : Icons.filter_list,
                color: _hasActiveFilters ? Colors.deepPurple : Colors.grey.shade600,
              ),
              tooltip: showFilters ? 'Hide Filters' : 'Show Filters',
            ),
          ),
          if (_hasActiveFilters)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: IconButton(
                onPressed: _clearFilters,
                icon: Icon(Icons.clear, color: Colors.red.shade400),
                tooltip: 'Clear Filters',
              ),
            ),
        ],
      ),
    );
  }

  bool get _hasActiveFilters {
    return selectedCategory != null || selectedMode != null || selectedLevel != null;
  }

  void _clearFilters() {
    setState(() {
      selectedCategory = null;
      selectedMode = null;
      selectedLevel = null;
    });
  }

  Widget _buildFilterPanel(bool isDarkMode) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.tune, color: Colors.deepPurple, size: 20),
              const SizedBox(width: 8),
              Text(
                'Filters',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              const Spacer(),
              if (_hasActiveFilters)
                TextButton(
                  onPressed: _clearFilters,
                  child: Text('Clear All', style: TextStyle(color: Colors.red.shade400)),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Filter dropdowns in a row
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown(
                  'Category',
                  selectedCategory,
                  categories,
                  (value) => setState(() => selectedCategory = value),
                  isDarkMode,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFilterDropdown(
                  'Mode',
                  selectedMode,
                  modes,
                  (value) => setState(() => selectedMode = value),
                  isDarkMode,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFilterDropdown(
                  'Level',
                  selectedLevel,
                  levels,
                  (value) => setState(() => selectedLevel = value),
                  isDarkMode,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String? selectedValue,
    List<String> options,
    Function(String?) onChanged,
    bool isDarkMode,
  ) {
    return DropdownButtonFormField<String>(
      value: selectedValue,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      dropdownColor: isDarkMode ? Colors.grey.shade800 : Colors.white,
      style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87, fontSize: 14),
      items: [
        DropdownMenuItem<String>(
          value: null,
          child: Text('All', style: TextStyle(color: Colors.grey.shade600, fontSize: 14)),
        ),
        ...options.map(
          (option) =>
              DropdownMenuItem<String>(value: option, child: Text(option, style: const TextStyle(fontSize: 14))),
        ),
      ],
      onChanged: onChanged,
    );
  }

  Map<String, Map<String, dynamic>> _groupSkillsByUser(List<QueryDocumentSnapshot> skills) {
    final Map<String, Map<String, dynamic>> userSkills = {};

    for (final doc in skills) {
      final data = doc.data() as Map<String, dynamic>;
      final userId = data['userId'] ?? '';

      // Skip empty user IDs but allow current user's skills to be visible
      if (userId.isEmpty) continue;

      if (!userSkills.containsKey(userId)) {
        userSkills[userId] = {
          'userId': userId,
          'userEmail': data['userEmail'] ?? 'Unknown',
          'offers': <Map<String, dynamic>>[],
          'requests': <Map<String, dynamic>>[],
        };
      }

      final skillData = {
        'id': doc.id,
        'title': data['title'] ?? 'Unknown Skill',
        'description': data['description'] ?? 'No description',
        'category': data['category'] ?? 'Other',
        'duration': data['duration'] ?? 'Not specified',
        'location': data['location'] ?? 'Not specified',
        'mode': data['mode'] ?? 'Not specified',
        'experienceLevel': data['experienceLevel'] ?? 'Any level',
        'timestamp': data['timestamp'],
      };

      if (data['type'] == 'offer') {
        userSkills[userId]!['offers'].add(skillData);
      } else if (data['type'] == 'request') {
        userSkills[userId]!['requests'].add(skillData);

        // Add exchange skill to offers if available
        if (data['exchangeSkill'] != null && data['exchangeSkill'].toString().isNotEmpty) {
          userSkills[userId]!['offers'].add({
            ...skillData,
            'id': '${doc.id}_exchange',
            'title': data['exchangeSkill'],
            'description': data['exchangeDescription'] ?? 'Exchange skill for ${skillData['title']}',
            'category': data['exchangeCategory'] ?? skillData['category'],
            'isExchange': true,
          });
        }
      }
    }

    return userSkills;
  }

  List<Map<String, dynamic>> _filterSkills(Map<String, Map<String, dynamic>> userSkills) {
    var filtered =
        userSkills.values.where((user) {
          final offers = user['offers'] as List<Map<String, dynamic>>;
          final requests = user['requests'] as List<Map<String, dynamic>>;

          // Must have at least one skill
          if (offers.isEmpty && requests.isEmpty) return false;

          // Search filter
          if (searchQuery.isNotEmpty) {
            final query = searchQuery.toLowerCase();
            final userEmail = user['userEmail'].toString().toLowerCase();

            final matchesUser = userEmail.contains(query);
            final matchesOffers = offers.any(
              (skill) =>
                  skill['title'].toString().toLowerCase().contains(query) ||
                  skill['description'].toString().toLowerCase().contains(query) ||
                  skill['category'].toString().toLowerCase().contains(query),
            );
            final matchesRequests = requests.any(
              (skill) =>
                  skill['title'].toString().toLowerCase().contains(query) ||
                  skill['description'].toString().toLowerCase().contains(query) ||
                  skill['category'].toString().toLowerCase().contains(query),
            );

            if (!matchesUser && !matchesOffers && !matchesRequests) return false;
          }

          // Category filter
          if (selectedCategory != null) {
            final hasMatchingCategory =
                offers.any((skill) => skill['category'] == selectedCategory) ||
                requests.any((skill) => skill['category'] == selectedCategory);
            if (!hasMatchingCategory) return false;
          }

          // Mode filter
          if (selectedMode != null) {
            final hasMatchingMode =
                offers.any((skill) => skill['mode'] == selectedMode) ||
                requests.any((skill) => skill['mode'] == selectedMode);
            if (!hasMatchingMode) return false;
          }

          // Level filter
          if (selectedLevel != null) {
            final hasMatchingLevel =
                offers.any((skill) => skill['experienceLevel'] == selectedLevel) ||
                requests.any((skill) => skill['experienceLevel'] == selectedLevel);
            if (!hasMatchingLevel) return false;
          }

          return true;
        }).toList();

    // Sort by most recent activity
    filtered.sort((a, b) {
      final aOffers = a['offers'] as List<Map<String, dynamic>>;
      final aRequests = a['requests'] as List<Map<String, dynamic>>;
      final bOffers = b['offers'] as List<Map<String, dynamic>>;
      final bRequests = b['requests'] as List<Map<String, dynamic>>;

      final aLatest = [...aOffers, ...aRequests]
          .map((skill) => skill['timestamp'] as Timestamp?)
          .where((t) => t != null)
          .map((t) => t!.toDate())
          .fold<DateTime?>(null, (latest, date) => latest == null || date.isAfter(latest) ? date : latest);

      final bLatest = [...bOffers, ...bRequests]
          .map((skill) => skill['timestamp'] as Timestamp?)
          .where((t) => t != null)
          .map((t) => t!.toDate())
          .fold<DateTime?>(null, (latest, date) => latest == null || date.isAfter(latest) ? date : latest);

      if (aLatest == null && bLatest == null) return 0;
      if (aLatest == null) return 1;
      if (bLatest == null) return -1;
      return bLatest.compareTo(aLatest);
    });

    return filtered;
  }

  Widget _buildUserSkillCard(Map<String, dynamic> userData, bool isDarkMode) {
    final userName = userData['userEmail'].toString().split('@')[0];
    final offers = userData['offers'] as List<Map<String, dynamic>>;
    final requests = userData['requests'] as List<Map<String, dynamic>>;
    final userId = userData['userId'] as String;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.08), blurRadius: 12, offset: const Offset(0, 4))],
        border: Border.all(color: AppTheme.borderColor, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced User header with rating and status
            Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 28,
                      backgroundColor: AppTheme.primaryGreen,
                      child: Text(
                        userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                        style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 20),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userName,
                        style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppTheme.textPrimary),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Row(
                            children: List.generate(
                              5,
                              (index) =>
                                  Icon(Icons.star, size: 16, color: index < 4 ? Colors.amber : Colors.grey.shade300),
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text('4.8', style: TextStyle(fontWeight: FontWeight.w600)),
                          const SizedBox(width: 4),
                          Text('(23 reviews)', style: TextStyle(color: AppTheme.textSecondary, fontSize: 12)),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${offers.length} skills offered • ${requests.length} learning',
                        style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(color: AppTheme.lightGreen, borderRadius: BorderRadius.circular(20)),
                      child: const Text(
                        'Pro',
                        style: TextStyle(color: AppTheme.primaryGreen, fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildActionButton(
                          icon: Icons.message_outlined,
                          label: 'Message',
                          onPressed: () => _openChat(userId, userName),
                          isPrimary: false,
                        ),
                        const SizedBox(width: 8),
                        _buildActionButton(
                          icon: Icons.calendar_today,
                          label: 'Book',
                          onPressed: () => _scheduleSession(userData),
                          isPrimary: true,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Enhanced Skills sections
            if (offers.isNotEmpty) ...[
              _buildEnhancedSkillSection('Teaching', offers, AppTheme.primaryGreen, Icons.school),
              if (requests.isNotEmpty) const SizedBox(height: 16),
            ],

            if (requests.isNotEmpty) ...[
              _buildEnhancedSkillSection('Learning', requests, AppTheme.accentBlue, Icons.lightbulb_outline),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    return SizedBox(
      height: 36,
      child:
          isPrimary
              ? ElevatedButton.icon(
                onPressed: onPressed,
                icon: Icon(icon, size: 16),
                label: Text(label),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  textStyle: const TextStyle(fontSize: 14),
                ),
              )
              : OutlinedButton.icon(
                onPressed: onPressed,
                icon: Icon(icon, size: 16),
                label: Text(label),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  textStyle: const TextStyle(fontSize: 14),
                ),
              ),
    );
  }

  Widget _buildEnhancedSkillSection(String title, List<Map<String, dynamic>> skills, Color color, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(8)),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: color)),
            const Spacer(),
            if (skills.length > 3)
              Text('+${skills.length - 3} more', style: const TextStyle(color: AppTheme.textSecondary, fontSize: 12)),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: skills.take(3).map((skill) => _buildEnhancedSkillChip(skill, color)).toList(),
        ),
      ],
    );
  }

  Widget _buildEnhancedSkillChip(Map<String, dynamic> skill, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getCategoryIcon(skill['category']), size: 14, color: color),
          const SizedBox(width: 6),
          Text(skill['title'], style: TextStyle(color: color, fontWeight: FontWeight.w500, fontSize: 13)),
          const SizedBox(width: 6),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(color: color.withValues(alpha: 0.15), borderRadius: BorderRadius.circular(10)),
            child: Text(
              skill['experienceLevel'] ?? 'Any',
              style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'programming':
        return Icons.code;
      case 'design':
        return Icons.palette;
      case 'languages':
        return Icons.language;
      case 'music':
        return Icons.music_note;
      case 'sports':
        return Icons.sports;
      case 'cooking':
        return Icons.restaurant;
      case 'photography':
        return Icons.camera_alt;
      case 'writing':
        return Icons.edit;
      case 'mathematics':
        return Icons.calculate;
      case 'science':
        return Icons.science;
      case 'art':
        return Icons.brush;
      default:
        return Icons.school;
    }
  }

  Widget _buildSkillSection(
    String title,
    List<Map<String, dynamic>> skills,
    Color color,
    bool isDarkMode,
    bool isOffer,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(isOffer ? Icons.volunteer_activism : Icons.help_outline, color: color, size: 18),
            const SizedBox(width: 8),
            Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: color)),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(10)),
              child: Text(
                '${skills.length}',
                style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Skills grid
        ...skills.map((skill) => _buildDetailedSkillCard(skill, color, isDarkMode)),
      ],
    );
  }

  Widget _buildDetailedSkillCard(Map<String, dynamic> skill, Color color, bool isDarkMode) {
    final isExchange = skill['isExchange'] == true;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and category
          Row(
            children: [
              if (isExchange) ...[Icon(Icons.swap_horiz, size: 16, color: color), const SizedBox(width: 4)],
              Expanded(
                child: Text(
                  skill['title'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(8)),
                child: Text(
                  skill['category'],
                  style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            skill['description'],
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
              height: 1.4,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // Details row
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildDetailChip(Icons.schedule, skill['duration'], color),
              _buildDetailChip(Icons.location_on, skill['location'], color),
              _buildDetailChip(Icons.computer, skill['mode'], color),
              _buildDetailChip(Icons.star, skill['experienceLevel'], color),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailChip(IconData icon, String text, Color color) {
    if (text == 'Not specified' || text.isEmpty) return const SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: color),
        const SizedBox(width: 4),
        Text(text, style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.w500)),
      ],
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
          const SizedBox(height: 16),
          const Text('Error loading skills', style: TextStyle(fontSize: 18)),
          const SizedBox(height: 8),
          Text(error, style: TextStyle(color: Colors.red.shade400), textAlign: TextAlign.center),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.school_outlined, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text('No skills found', style: TextStyle(fontSize: 18, color: isDarkMode ? Colors.white : Colors.black87)),
          const SizedBox(height: 8),
          Text(
            searchQuery.isNotEmpty ? 'Try adjusting your search or filters' : 'Be the first to share your skills!',
            style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _openChat(String contactId, String contactName) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => ChatScreen(contactName: contactName, contactId: contactId)));
  }

  void _scheduleSession(Map<String, dynamic> userData) {
    final userName = userData['userEmail'].toString().split('@')[0];
    final userId = userData['userId'] as String;
    final offers = userData['offers'] as List<Map<String, dynamic>>;

    if (offers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No skills available for scheduling'), backgroundColor: Colors.orange),
      );
      return;
    }

    // Use the first non-exchange skill for scheduling
    final firstSkill = offers.firstWhere((skill) => skill['isExchange'] != true, orElse: () => offers.first);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => SessionSchedulingScreen(
              skillId: firstSkill['id'],
              skillTitle: firstSkill['title'],
              providerId: userId,
              providerName: userName,
            ),
      ),
    );
  }
}
