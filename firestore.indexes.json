{"indexes": [{"collectionGroup": "skills", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "skills", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "skills", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "chats", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "providerId", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "ASCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "requesterId", "order": "ASCENDING"}, {"fieldPath": "dateTime", "order": "ASCENDING"}]}, {"collectionGroup": "items", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sessionId", "order": "ASCENDING"}, {"fieldPath": "reviewerId", "order": "ASCENDING"}]}], "fieldOverrides": []}