import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirestoreStreamService {
  static final FirestoreStreamService _instance = FirestoreStreamService._internal();
  factory FirestoreStreamService() => _instance;
  FirestoreStreamService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Stream controllers for different data types
  final Map<String, StreamController<QuerySnapshot>> _streamControllers = {};
  final Map<String, StreamSubscription<QuerySnapshot>> _subscriptions = {};

  // Get or create a unique stream for skills
  Stream<QuerySnapshot> getSkillsStream(
    String streamId, {
    String? type,
    String? userId,
    bool excludeCurrentUser = false,
  }) {
    final key = 'skills_$streamId';

    // Safely check before adding a stream - prevent duplicate controllers
    if (!_streamControllers.containsKey(key) || _streamControllers[key]!.isClosed) {
      final controller = StreamController<QuerySnapshot>.broadcast();
      _streamControllers[key] = controller;
    } else {
      return _streamControllers[key]!.stream;
    }

    final controller = _streamControllers[key]!;
    Query query = _firestore.collection('posts');

    // Simplified query to avoid complex index requirements
    // Prioritize user-specific queries for skill management
    if (userId != null && type != null) {
      // For user + type queries (like skill management), use both filters
      // This requires the composite index we created
      query = query.where('userId', isEqualTo: userId);
      query = query.where('type', isEqualTo: type);
      query = query.orderBy('timestamp', descending: true);
    } else if (userId != null) {
      // For user-specific queries, only filter by userId
      query = query.where('userId', isEqualTo: userId);
      query = query.orderBy('timestamp', descending: true);
    } else if (type != null) {
      // For type-specific queries, only filter by type
      query = query.where('type', isEqualTo: type);
      query = query.orderBy('timestamp', descending: true);
    } else {
      // For general queries, just order by timestamp
      query = query.orderBy('timestamp', descending: true);
    }

    final subscription = query.snapshots().listen(
      (snapshot) {
        if (!controller.isClosed) {
          if (excludeCurrentUser) {
            final currentUserId = _auth.currentUser?.uid;
            final filteredDocs =
                snapshot.docs.where((doc) {
                  final data = doc.data() as Map<String, dynamic>;
                  return data['userId'] != currentUserId;
                }).toList();

            // Create a new QuerySnapshot with filtered docs
            controller.add(_createFilteredSnapshot(snapshot, filteredDocs));
          } else {
            controller.add(snapshot);
          }
        }
      },
      onError: (error) {
        if (!controller.isClosed) {
          controller.addError(error);
        }
      },
    );

    _subscriptions[key] = subscription;

    return controller.stream;
  }

  // Get or create a unique stream for sessions
  Stream<QuerySnapshot> getSessionsStream(String streamId) {
    final key = 'sessions_$streamId';

    // Safely check before adding a stream - prevent duplicate controllers
    if (!_streamControllers.containsKey(key) || _streamControllers[key]!.isClosed) {
      final controller = StreamController<QuerySnapshot>.broadcast();
      _streamControllers[key] = controller;
    } else {
      return _streamControllers[key]!.stream;
    }

    final controller = _streamControllers[key]!;

    final subscription = _firestore
        .collection('sessions')
        .orderBy('dateTime', descending: false)
        .snapshots()
        .listen(
          (snapshot) {
            if (!controller.isClosed) {
              controller.add(snapshot);
            }
          },
          onError: (error) {
            if (!controller.isClosed) {
              controller.addError(error);
            }
          },
        );

    _subscriptions[key] = subscription;

    return controller.stream;
  }

  // Get or create a unique stream for chats
  Stream<QuerySnapshot> getChatsStream(String streamId, String userId) {
    final key = 'chats_${streamId}_$userId';

    // Safely check before adding a stream - prevent duplicate controllers
    if (!_streamControllers.containsKey(key) || _streamControllers[key]!.isClosed) {
      final controller = StreamController<QuerySnapshot>.broadcast();
      _streamControllers[key] = controller;
    } else {
      return _streamControllers[key]!.stream;
    }

    final controller = _streamControllers[key]!;

    final subscription = _firestore
        .collection('chats')
        .where('participants', arrayContains: userId)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .listen(
          (snapshot) {
            if (!controller.isClosed) {
              controller.add(snapshot);
            }
          },
          onError: (error) {
            if (!controller.isClosed) {
              controller.addError(error);
            }
          },
        );

    _subscriptions[key] = subscription;

    return controller.stream;
  }

  // Get or create a unique stream for messages
  Stream<QuerySnapshot> getMessagesStream(String streamId, String chatId) {
    final key = 'messages_${streamId}_$chatId';

    // Safely check before adding a stream - prevent duplicate controllers
    if (!_streamControllers.containsKey(key) || _streamControllers[key]!.isClosed) {
      final controller = StreamController<QuerySnapshot>.broadcast();
      _streamControllers[key] = controller;
    } else {
      return _streamControllers[key]!.stream;
    }

    final controller = _streamControllers[key]!;

    final subscription = _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .listen(
          (snapshot) {
            if (!controller.isClosed) {
              controller.add(snapshot);
            }
          },
          onError: (error) {
            if (!controller.isClosed) {
              controller.addError(error);
            }
          },
        );

    _subscriptions[key] = subscription;

    return controller.stream;
  }

  // Get or create a unique stream for notifications
  Stream<QuerySnapshot> getNotificationsStream(String streamId, String userId) {
    final key = 'notifications_${streamId}_$userId';

    // Safely check before adding a stream - prevent duplicate controllers
    if (!_streamControllers.containsKey(key) || _streamControllers[key]!.isClosed) {
      final controller = StreamController<QuerySnapshot>.broadcast();
      _streamControllers[key] = controller;
    } else {
      return _streamControllers[key]!.stream;
    }

    final controller = _streamControllers[key]!;

    final subscription = _firestore
        .collection('notifications')
        .doc(userId)
        .collection('items')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .listen(
          (snapshot) {
            if (!controller.isClosed) {
              controller.add(snapshot);
            }
          },
          onError: (error) {
            if (!controller.isClosed) {
              controller.addError(error);
            }
          },
        );

    _subscriptions[key] = subscription;

    return controller.stream;
  }

  // Get or create a unique stream for contacts/users
  Stream<QuerySnapshot> getContactsStream(String streamId) {
    final key = 'contacts_$streamId';

    // Safely check before adding a stream - prevent duplicate controllers
    if (!_streamControllers.containsKey(key) || _streamControllers[key]!.isClosed) {
      final controller = StreamController<QuerySnapshot>.broadcast();
      _streamControllers[key] = controller;
    } else {
      return _streamControllers[key]!.stream;
    }

    final controller = _streamControllers[key]!;

    final subscription = _firestore
        .collection('users')
        .orderBy('name')
        .snapshots()
        .listen(
          (snapshot) {
            if (!controller.isClosed) {
              controller.add(snapshot);
            }
          },
          onError: (error) {
            if (!controller.isClosed) {
              controller.addError(error);
            }
          },
        );

    _subscriptions[key] = subscription;

    return controller.stream;
  }

  // Helper method to create a filtered QuerySnapshot
  QuerySnapshot _createFilteredSnapshot(QuerySnapshot original, List<QueryDocumentSnapshot> filteredDocs) {
    // This is a simplified approach - in a real implementation you might need a more sophisticated solution
    return _FilteredQuerySnapshot(original, filteredDocs);
  }

  // Clean up a specific stream
  void disposeStream(String streamId) {
    final keys = _streamControllers.keys.where((key) => key.contains(streamId)).toList();

    for (final key in keys) {
      _subscriptions[key]?.cancel();
      _streamControllers[key]?.close();
      _subscriptions.remove(key);
      _streamControllers.remove(key);
    }
  }

  // Clean up all streams
  void disposeAll() {
    for (final subscription in _subscriptions.values) {
      subscription.cancel();
    }
    for (final controller in _streamControllers.values) {
      controller.close();
    }
    _subscriptions.clear();
    _streamControllers.clear();
  }
}

// Custom QuerySnapshot implementation for filtered results
class _FilteredQuerySnapshot implements QuerySnapshot {
  final QuerySnapshot _original;
  final List<QueryDocumentSnapshot> _docs;

  _FilteredQuerySnapshot(this._original, this._docs);

  @override
  List<QueryDocumentSnapshot> get docs => _docs;

  @override
  List<DocumentChange> get docChanges => _original.docChanges;

  @override
  SnapshotMetadata get metadata => _original.metadata;

  @override
  int get size => _docs.length;
}
